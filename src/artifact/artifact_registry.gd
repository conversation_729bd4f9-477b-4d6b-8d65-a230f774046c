extends Node

const ARTIFACTS_PATH = "res://resources/artifacts/"
var _all_artifacts: Array[ArtifactData] = []

func _ready() -> void:
	_load_all_artifacts_from_path(ARTIFACTS_PATH)

func _load_all_artifacts_from_path(path: String) -> void:
	var dir := DirAccess.open(path)
	if dir == null:
		return
	dir.list_dir_begin()
	var file_name := dir.get_next()
	while file_name != "":
		if not dir.current_is_dir() and file_name.ends_with(".tres"):
			var res := load(path + file_name)
			if res is ArtifactData:
				_all_artifacts.append(res)
		file_name = dir.get_next()
	dir.list_dir_end()

func get_random_unowned_artifacts(count: int) -> Array[ArtifactData]:
	var unowned: Array[ArtifactData] = []
	for a in _all_artifacts:
		if not RunStateService.has_artifact(a.id):
			unowned.append(a)
	unowned.shuffle()
	if count >= unowned.size():
		return unowned
	return unowned.slice(0, count)
