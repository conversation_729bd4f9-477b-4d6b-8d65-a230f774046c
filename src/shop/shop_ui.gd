class_name ShopUI
extends Control

@export var currency_label: Label
@export var items_container: Container
@export var item_panel_scene: PackedScene
@export var player_service: PlayerService
@export var close_button: Button
@onready var artifact_registry: ArtifactRegistry = get_node("/root/ArtifactRegistry") as ArtifactRegistry


var _panels: Array[ShopItemPanel] = []

func _ready() -> void:
	close_button.pressed.connect(_on_close_pressed)
	hide()

func show_ui() -> void:
	visible = true
	_rebuild_items()
	GameStateService.block_gameplay_input()

func hide_ui() -> void:
	visible = false
	GameStateService.enable_gameplay_input()

func _rebuild_items() -> void:
	_clear_items()
	var artifacts: Array[ArtifactData] = artifact_registry.get_random_unowned_artifacts(3)
	for artifact_data in artifacts:
		var panel: ShopItemPanel = item_panel_scene.instantiate() as ShopItemPanel
		panel.setup(artifact_data)
		panel.purchase_requested.connect(_on_purchase_requested)
		_panels.append(panel)
		items_container.add_child(panel)
	_update_currency()

func _clear_items() -> void:
	for p: ShopItemPanel in _panels:
		p.queue_free()
	_panels.clear()

	for child in items_container.get_children():
		child.queue_free()

func _update_currency() -> void:
	currency_label.text = "Валюта: " + str(RunStateService.get_total_painted_tiles())
	for p: ShopItemPanel in _panels:
		p._update_button()

func _on_purchase_requested(artifact: ArtifactData) -> void:
	if artifact == null:
		return
	if RunStateService.spend_painted_tiles(artifact.cost):
		var inv: ArtifactInventoryComponent = player_service.get_artifact_inventory_component()
		inv.add_artifact(artifact)
		_update_currency()
		for p: ShopItemPanel in _panels:
			if is_instance_valid(p) and p.get_artifact() != null and p.get_artifact().id == artifact.id:
				p.mark_purchased()
				break

func _on_close_pressed() -> void:
	hide_ui()
