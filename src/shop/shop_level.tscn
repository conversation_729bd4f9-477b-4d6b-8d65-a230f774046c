[gd_scene load_steps=18 format=4 uid="uid://cvmn3d8forhd5"]

[ext_resource type="Script" uid="uid://b5845teirfvi" path="res://src/shop/shop_level.gd" id="1_shop_level"]
[ext_resource type="PackedScene" uid="uid://2o2nqedmdng0" path="res://src/color_tile/color_tile.tscn" id="2_color_tile"]
[ext_resource type="PackedScene" uid="uid://c8lam3n4p5q6r" path="res://src/hub/portal.tscn" id="3_portal"]
[ext_resource type="PackedScene" uid="uid://dfy35tmjoeoht" path="res://src/water_tile/water_tile.tscn" id="3_wsa7y"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="4_0q3nj"]
[ext_resource type="PackedScene" uid="uid://biw4o1x1u6l4i" path="res://src/tile_query_system/tile_query_system.tscn" id="4_tile_query"]
[ext_resource type="Script" uid="uid://cndyjg0c5r3k4" path="res://src/player/player_service.gd" id="5_player_service"]
[ext_resource type="PackedScene" uid="uid://bg26gm6hjnwbj" path="res://src/shop/shop_ui.tscn" id="6_shop_ui"]
[ext_resource type="PackedScene" uid="uid://d4jdklgcrhr5l" path="res://src/shop/shop_npc.tscn" id="7_shop_npc"]
[ext_resource type="PackedScene" uid="uid://bi0dhs7jf7yrl" path="res://src/movement/movement_system.tscn" id="9_3ydos"]
[ext_resource type="PackedScene" uid="uid://5agsxx7ekdi6" path="res://src/player/player_input_system.tscn" id="10_n1vhf"]
[ext_resource type="PackedScene" uid="uid://dmr0fcamx7t56" path="res://addons/virtual_joystick/virtual_joystick_scene.tscn" id="11_keylo"]
[ext_resource type="PackedScene" uid="uid://q1f8b424mhr2" path="res://src/artifact_ui/artifacts_display.tscn" id="13_0q3nj"]

[sub_resource type="TileSetScenesCollectionSource" id="TileSetScenesCollectionSource_ejrr5"]
scenes/1/scene = ExtResource("2_color_tile")
scenes/2/scene = ExtResource("3_wsa7y")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_5rm4c"]
texture = ExtResource("4_0q3nj")
texture_region_size = Vector2i(8, 8)
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
2:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:0/0 = 0
0:1/0 = 0
0:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
1:1/0 = 0
1:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
2:1/0 = 0
2:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)
3:1/0 = 0
3:1/0/physics_layer_1/polygon_0/points = PackedVector2Array(-4, -4, 4, -4, 4, 4, -4, 4)

[sub_resource type="TileSet" id="TileSet_of88e"]
tile_size = Vector2i(8, 8)
physics_layer_0/collision_layer = 1
physics_layer_1/collision_layer = 2
physics_layer_1/collision_mask = 0
sources/1 = SubResource("TileSetAtlasSource_5rm4c")
sources/0 = SubResource("TileSetScenesCollectionSource_ejrr5")

[sub_resource type="CircleShape2D" id="CircleShape2D_ed17i"]
radius = 1.35208

[node name="ShopLevel" type="Node2D" node_paths=PackedStringArray("exit_portal", "shop_ui", "tile_query_system", "player_service")]
scale = Vector2(8, 8)
script = ExtResource("1_shop_level")
exit_portal = NodePath("Portal")
shop_ui = NodePath("CanvasLayer/ShopUI")
tile_query_system = NodePath("Systems/TileQuerySystem")
player_service = NodePath("Services/PlayerService")

[node name="TileMapLayer" type="TileMapLayer" parent="."]
texture_filter = 1
position = Vector2(0, -0.875)
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_of88e")

[node name="ShopNpc" parent="." node_paths=PackedStringArray("shop_ui") instance=ExtResource("7_shop_npc")]
position = Vector2(20, 74.375)
shop_ui = NodePath("../CanvasLayer/ShopUI")

[node name="CollisionShape2D" type="CollisionShape2D" parent="ShopNpc"]
position = Vector2(7.625, 0)
shape = SubResource("CircleShape2D_ed17i")

[node name="Portal" parent="." instance=ExtResource("3_portal")]
position = Vector2(59.875, 114)

[node name="Services" type="Node" parent="."]

[node name="PlayerService" type="Node" parent="Services"]
script = ExtResource("5_player_service")
metadata/_custom_type_script = "uid://cndyjg0c5r3k4"

[node name="Systems" type="Node" parent="."]

[node name="TileQuerySystem" parent="Systems" instance=ExtResource("4_tile_query")]

[node name="MovementSystem" parent="Systems" node_paths=PackedStringArray("player_service", "tile_query_system") instance=ExtResource("9_3ydos")]
player_service = NodePath("../../Services/PlayerService")
tile_query_system = NodePath("../TileQuerySystem")

[node name="PlayerInputSystem" parent="Systems" node_paths=PackedStringArray("movement_system", "player_service") instance=ExtResource("10_n1vhf")]
movement_system = NodePath("../MovementSystem")
player_service = NodePath("../../Services/PlayerService")

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="Virtual Joystick" parent="CanvasLayer" instance=ExtResource("11_keylo")]
anchors_preset = 15
anchor_top = 0.0
anchor_right = 1.0
offset_top = 0.0
offset_right = 0.0
offset_bottom = 0.0
grow_horizontal = 2
grow_vertical = 2
deadzone_size = 40.0
joystick_mode = 2
visibility_mode = 3
action_left = "move_left"
action_right = "move_right"
action_up = "move_up"
action_down = "move_down"

[node name="ShopUI" parent="CanvasLayer" node_paths=PackedStringArray("player_service") instance=ExtResource("6_shop_ui")]
visible = false
player_service = NodePath("../../Services/PlayerService")

[node name="ArtifactsDisplay" parent="." node_paths=PackedStringArray("player_service") instance=ExtResource("13_0q3nj")]
offset_left = 0.25
offset_top = 183.875
offset_right = 0.25
offset_bottom = 183.875
player_service = NodePath("../Services/PlayerService")

[editable path="CanvasLayer/Virtual Joystick"]
